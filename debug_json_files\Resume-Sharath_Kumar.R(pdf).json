{
  "name": "<PERSON><PERSON><PERSON>",
  "email": null,
  "phone": null,
  "education": [
    {
      "degree": "Bachelor of Science (Data Science)",
      "institution": "Sarada Vilas College",
      "year": "2021 - 2025"
    },
    {
      "degree": "Intermediate (Class XII)",
      "institution": "Soundarya Composite PU College",
      "year": "2018 - 2020"
    },
    {
      "degree": "Matriculation (Class X)",
      "institution": "Guru Shree Vidya Kendra",
      "year": "2008 - 2018"
    }
  ],
  "skills": [
    "C++",
    "Python",
    "Machine Learning",
    "Power BI",
    "Excel",
    "MySQL",
    "Matplotlib",
    "Pandas",
    "Data Structure",
    "Communication Skills",
    "Decision-making",
    "Analytics",
    "Spreadsheet",
    "Microsoft Oce"
  ],
  "experience": [
    {
      "company_name": "Think Beyond Mysore",
      "role": "Assistant Robotic Intern",
      "duration": "April 2024 - October 2024",
      "key_responsibilities": "Supported the development of IoT-based projects and nal-year academic projects for various colleges, and assisted in designing and implementing IoT solutions for real-world applications. Provided technical guidance and collaborated with students and teams of several colleges that ensured project success around 80%. Conducted testing and troubleshooting of IoT devices and systems, thus documented project details and outcomes for reference and knowledge sharing."
    }
  ],
  "projects": [
    {
      "name": "IPL RCB Stratergy",
      "description": "Evaluated IPL players based on key performance metrics such as batting average, strike rate, economy rate, and wickets. Utilized SQL queries to retrieve, clean, and preprocess IPL player data for analysis. Applied ranking functions and conditional ltering to identify undervalued players. Provided data-driven recommendations for optimal batting order and bowling combinations."
    },
    {
      "name": "IT Ticket Analysis",
      "description": "Conducted data analysis using Excel to assess IT agent performance and identify high and low performers. Utilized Pivot Tables, Charts, and Slicers to evaluate ticket resolution eciency and detect bottlenecks. Analyzed employee satisfaction ratings and workload distribution through data visualization. Provided actionable insights for hiring, training, and resource allocation to enhance service quality. Recommended process optimizations to improve overall IT support eciency. Identied workload imbalances and ineficiencies, supporting data-driven decisions for IT support improvements."
    }
  ],
  "certifications": [
    {
      "name": "Professional Certiﬁcate in Data Science",
      "institution": "Newton School",
      "year": "September 2024",
      "description": "Worked on real-world datasets, performed data cleaning, analysis, and visualization using Excel, SQL and Power BI. Applied techniques to extract insights, created dashboards and generate reports for data decision making."
    }
  ],
  "domain_of_interest": [
    "Data Science",
    "Data Analysis",
    "Machine Learning",
    "IoT"
  ],
  "languages_known": [
    "English",
    "Tamil"
  ],
  "achievements": [],
  "publications": [],
  "volunteer_experience": [],
  "references": [],
  "summary": null,
  "personal_projects": [],
  "social_media": [
    null,
    null
  ]
}