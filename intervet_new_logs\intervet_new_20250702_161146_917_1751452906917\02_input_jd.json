{"job_title": "Python NLP Engineer for Generative AI", "company_name": "ELTEMATE", "location": "Amsterdam (Netherlands) or EU", "job_type": "Full-time", "work_mode": "Hybrid", "department": "AI Development", "summary": "We are seeking a talented and experienced Python NLP Engineer with a Generative AI focus to join our growing team.", "responsibilities": ["Design, develop, and maintain sophisticated NLP and Generative AI applications in Python.", "Design and implement back-end APIs to deliver Python services to front-end applications using FastAPI and Docker.", "Write clean, efficient, and modular code adhering to best practices and coding standards.", "Optimize application performance and ensure scalability.", "Collaborate in the database design and management of those systems (PostgreSQL or MongoDB).", "Conduct thorough testing and debugging to identify and resolve issues."], "required_skills": ["Python", "NLP", "Generative AI", "FastAPI", "<PERSON>er", "PostgreSQL", "MongoDB", "<PERSON><PERSON><PERSON><PERSON>", "PyTorch", "GPT", "Transformers"], "preferred_skills": ["Semantic search", "Document retrieval", "Discriminative AI (classifiers)", "F1", "Precision", "Recall", "JavaScript"], "required_experience": "2+ years", "education_requirements": ["Bachelor's degree in Computer Science, Engineering, Computational Linguistics, Data Science or a related quantitative field", "Equivalent experience"], "education_details": {"degree_level": "Bachelor's", "field_of_study": "Computer Science, Engineering, Computational Linguistics, Data Science or a related quantitative field", "is_required": true, "alternatives": "Equivalent experience"}, "salary_range": "€ 70,000 – €90,000", "benefits": [], "requirements": [], "application_deadline": null, "posting_date": "December 2023", "contact_information": null, "company_description": null, "industry": "Legal Tech", "career_level": "Mid level", "confidence_score": 0.89, "confidence_details": {}}