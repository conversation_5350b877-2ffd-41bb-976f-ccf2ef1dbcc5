{"event": "session_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "timestamp": "2025-07-04T15:45:52.683243", "message": "New API session started"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "eaefe81e-8fec-4c0e-ab4c-dd58da4a31f5", "endpoint": "/", "timestamp": "2025-07-04T15:45:55.868397", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "eaefe81e-8fec-4c0e-ab4c-dd58da4a31f5", "endpoint": "/", "timestamp": "2025-07-04T15:45:55.870402", "total_time_seconds": 0.002005338668823242, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "5239f9b4-b129-4425-a6cc-aa8ed23272cd", "endpoint": "/favicon.ico", "timestamp": "2025-07-04T15:45:55.945450", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "5239f9b4-b129-4425-a6cc-aa8ed23272cd", "endpoint": "/favicon.ico", "timestamp": "2025-07-04T15:45:55.946449", "total_time_seconds": 0.00099945068359375, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "4e49a98f-129f-4251-b2a6-74dcf8164159", "endpoint": "/docs", "timestamp": "2025-07-04T15:45:59.466859", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "4e49a98f-129f-4251-b2a6-74dcf8164159", "endpoint": "/docs", "timestamp": "2025-07-04T15:45:59.467859", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "2ebaaeb6-034e-4cea-852a-683cc12d2022", "endpoint": "/openapi.json", "timestamp": "2025-07-04T15:45:59.544026", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "2ebaaeb6-034e-4cea-852a-683cc12d2022", "endpoint": "/openapi.json", "timestamp": "2025-07-04T15:45:59.568025", "total_time_seconds": 0.023998737335205078, "status_code": 200, "message": "Request completed in 0.0240s with status 200"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:46:49.228579", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:46:49.257593", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:46:49.258592", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:46:49.258592", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:46:49.258592", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:46:49.258592", "file_processing_time": 0.02301502227783203, "message": "Custom metric: file_processing_time=0.02301502227783203"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "69cbf357-c81c-4546-85e2-c8cad886a0cb", "endpoint": "/hybrid_resume", "timestamp": "2025-07-04T15:47:06.750812", "total_time_seconds": 17.52223229408264, "status_code": 200, "message": "Request completed in 17.5222s with status 200"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "53fc0426-ca3d-4082-8b56-c0fe8bde393a", "endpoint": "/jd_parser", "timestamp": "2025-07-04T15:47:12.787307", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "53fc0426-ca3d-4082-8b56-c0fe8bde393a", "endpoint": "/jd_parser", "timestamp": "2025-07-04T15:47:24.335283", "total_time_seconds": 11.547976016998291, "status_code": 200, "message": "Request completed in 11.5480s with status 200"}
{"event": "request_start", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.738326", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.740324", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.748325", "final_score": 5.67, "message": "Custom metric: final_score=5.67"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.749324", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.749324", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.749324", "log_folder": "intervet_new_logs\\intervet_new_20250704_154931_743_1751624371743", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250704_154931_743_1751624371743"}
{"event": "request_complete", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "request_id": "e2d40d1d-cce1-48c4-bf84-958af9b86921", "endpoint": "/intervet_new", "timestamp": "2025-07-04T15:49:31.749324", "total_time_seconds": 0.010997772216796875, "status_code": 200, "message": "Request completed in 0.0110s with status 200"}
{"event": "session_end", "session_id": "4c2ba1fc-819e-41e8-b94c-c91452e86f4c", "timestamp": "2025-07-04T15:50:54.555766", "message": "API session ended"}
