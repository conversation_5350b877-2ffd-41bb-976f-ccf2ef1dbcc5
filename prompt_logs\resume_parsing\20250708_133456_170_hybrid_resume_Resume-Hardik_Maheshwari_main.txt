================================================================================
LLM CALL LOG - 2025-07-08 13:34:56
================================================================================

[CALL INFORMATION]
Endpoint: /hybrid_resume
Context: resumes for testing/Resume-<PERSON>ik Ma<PERSON>hwari.pdf
Call Type: main
Model: gemma3:4b
Timestamp: 2025-07-08T13:34:56.170783
Metadata: {
  "timeout_seconds": 120,
  "max_tokens": 1000,
  "processing_time": 15.11598253250122,
  "has_image": false,
  "prompt_length": 7378,
  "response_length": 3388,
  "eval_count": 879,
  "prompt_eval_count": 1831,
  "model_total_duration": 15102364000
}

[PROMPT]
Length: 7378 characters
----------------------------------------

    🚨 CRITICAL FORMATTING RULES - VIOLATION WILL CAUSE SYSTEM FAILURE:
    - NEVER use ```json or ``` or any markdown formatting
    - NEVER add explanations, comments, or extra text before or after JSON
    - NEVER use code blocks, backticks, or markdown syntax
    - Your response must START IMMEDIATELY with { (opening brace)
    - Your response must END IMMEDIATELY with } (closing brace)
    - Return ONLY the JSON object, absolutely nothing else
    - Any markdown formatting will cause parsing failure and data loss
    - This is a machine-to-machine interface - human formatting is forbidden

    You are an expert resume parser. Extract ALL information from the resume sections below and return it as a clean JSON object.

    CRITICAL SCHEMA REQUIREMENTS:
    1. Extract ALL information that is explicitly mentioned in the resume sections.
    2. Format your response as a valid JSON object with EXACTLY the following structure:

    {
        "name": "Full Name",
        "email": "<EMAIL>" or null,
        "phone": "+1234567890" or null,
        "education": [
            {
                "degree": "Full Degree Name (Including Specialization)",
                "institution": "Institution Name",
                "year": "Year or Date Range",
                "grade": "GPA/Grade/Percentage if mentioned (e.g., '3.8/4.0', '85%', 'First Class', 'A Grade') or null if not mentioned"
            }
        ],
        "highest_education": "Highest level of education qualification (e.g., 'Bachelor of Technology', 'Master of Science', 'PhD in Computer Science') or null if no education found",
        "skills": ["Skill 1", "Skill 2", "Skill 3", ...],
        "experience": [
            {
                "company_name": "Company Name with Location if mentioned",
                "role": "Job Title",
                "duration": "Date Range",
                "key_responsibilities": "Detailed description of responsibilities and achievements"
            }
        ],
        "projects": [
            {
                "name": "Project Name",
                "description": "Detailed project description including technologies used"
            }
        ],
        "certifications": ["Certification Name 1", "Certification Name 2", ...],
        "domain_of_interest": ["Interest 1", "Interest 2", ...],
        "languages_known": ["Language 1", "Language 2", ...],
        "achievements": ["Achievement 1", "Achievement 2", ...],
        "publications": ["Publication 1", "Publication 2", ...],
        "volunteer_experience": ["Volunteer Experience 1", "Volunteer Experience 2", ...],
        "references": [],
        "summary": "Summary text or null",
        "personal_projects": [],
        "social_media": ["platform1.com/username", "platform2.com/username"]
    }

    STRICT FORMATTING RULES:
    3. For arrays, if no information is available, use an empty array []
    4. For string fields, if no information is available, use null
    5. Do not make up or infer information that is not explicitly stated in the resume
    6. Ensure the JSON is properly formatted and valid
    7. CRITICAL: Keep skills as a simple array of strings, not as objects or dictionaries
    8. CRITICAL: Keep certifications as a simple array of strings, not as objects
    9. CRITICAL: Keep achievements as a simple array of strings, not as objects
    10. CRITICAL: For experience entries, include all details in the key_responsibilities field as a single string with line breaks (\n)
    11. CRITICAL: For projects, include all details in the description field as a single string with line breaks (\n)
    12. CRITICAL: Extract the name, email, and phone from the CONTACT INFORMATION section if available

    CONTENT CLASSIFICATION RULES:
    13. EXPERIENCE section should contain ONLY professional work experience with companies/organizations
    14. EXTRA-CURRICULAR ACTIVITIES, sports, competitions, awards should go in ACHIEVEMENTS array
    15. CERTIFICATIONS should be simple strings like "Python for Beginners - Newton School (2024)"
    16. Do NOT create experience entries for activities that are not professional work
    17. Personal activities, sports, competitions, olympiads should be in achievements, not experience

    EDUCATION EXTRACTION RULES:
    18. For each education entry, extract GPA/grades/percentage if mentioned (e.g., "3.8/4.0", "85%", "First Class", "A Grade", "CGPA: 8.5/10")
    19. If no GPA/grade is mentioned for an education entry, set grade field to null
    20. For highest_education field, determine the highest level of education from all entries:
        - PhD/Doctorate > Master's/Postgraduate > Bachelor's/Undergraduate > Diploma/Certificate > High School
        - Include the full degree name with specialization if available
        - If no education is found, set to null

    Resume Sections:
    CONTACT INFORMATION:
Hardik Maheshwari
LinkedIn Github Leetcode

SUMMARY:
Aspiring Algo Trader & Frontend Developer with real startup internships under my belt. Built production-level tools, live
trading apps, and crushed 75+ LeetCode problems.

EDUCATION:
Bachelor of Technology (Articial intelligence) 2024 - 2028
Newton School Of Technology , Rishihood University Grade: 7.0/10.0
Matriculation (Class X) 2024 - 2024
Global Public School Grade: 88.0%
Intermediate (Class XII) 2021 - 2022
Global Public School Grade: 80.0%
INTERNSHIPS
Mobile Application Development June 2022 - August 2022
11Mantras Remote
Completed 2-month internship at11mantras , an astrologer-based startup.
Developed iOS
Android apps , gaining hands-on mobile development experience.
Contributed to the success of the project through key development eorts.

SKILLS:
Computer Languages: Python, JavaScript
Software Packages: Chart.js, React
Soft Skills: Leadership
Others: Analytics
EXTRA-CURRICULAR ACTIVITIES
Awarded `Best Boy of the Year' for leadership
achievements.
Achieved 1200+ rating on Chess.com (Blitz
Rapid formats).
Built Rs.6.2L+ stock portfolio using self-developed analysis tools.
Interned at 11mantras; developed production-level Android
iOS apps.

PROJECTS:
Live Trading , ( Github ) ( Demo ) April 2025
Tech stack: React, FastAPI, yFinance, Plotly.js, Styled-components
description:
{Market movers section with live indices, top gainers/losers
{Interactive candlestick charts
{Backend caching for fast performance
Key Features:
Market Movers Section: Displays live updates of Nifty, Sensex, and other indices, along with top gainers and losers.
Interactive Candlestick Charts: Custom-built charting using Plotly.js with support for indicators, zoom, pan, and
crosshair tracking.
Backend Caching: Implemented smart caching in FastAPI for high-speed data retrieval and reduced API calls.
Responsive UI: Built with styled-components for seamless experience across devices.
.
Best SMA combo , ( Github ) February 2025
 Tech Stack :- Python (AngleOne API, pandas,numpy, webSockets, Plotly) , Jupiter Notebook
Description :- SMA Combo is a stock analysis tool for testing short
long SMA combinations across stocks and timeframes. It oers backtesting, performance metrics, and
visualization , helping traders optimize strategies eciently
Features :- Custom SMA selection, multi-timeframe backtesting, performance metrics, chart visualization, and fast data
caching.



    JSON object (start with { immediately):
    
----------------------------------------

[RESPONSE]
Length: 3388 characters
----------------------------------------
```json
{
    "name": "Hardik Maheshwari",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (Artiﬁcial intelligence)",
            "institution": "Newton School Of Technology, Rishihood University",
            "year": "2024 - 2028",
            "grade": "7.0/10.0"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "Global Public School",
            "year": "2024 - 2024",
            "grade": "88.0%"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "Global Public School",
            "year": "2021 - 2022",
            "grade": "80.0%"
        }
    ],
    "highest_education": "Bachelor of Technology (Artiﬁcial intelligence)",
    "skills": [
        "Python",
        "JavaScript",
        "Chart.js",
        "React",
        "FastAPI",
        "yFinance",
        "Plotly.js",
        "Styled-components",
        "AngularOne API",
        "pandas",
        "numpy",
        "webSockets"
    ],
    "experience": [
        {
            "company_name": "11Mantras Remote",
            "role": "Mobile Application Development Intern",
            "duration": "June 2022 - August 2022",
            "key_responsibilities": "Completed 2-month internship at11mantras , an astrologer-based startup. Developed iOS Android apps , gaining hands-on mobile development experience. Contributed to the success of the project through key development eﬀorts."
        }
    ],
    "projects": [
        {
            "name": "Live Trading",
            "description": "Market movers section with live indices, top gainers/losers\nInteractive candlestick charts\nBackend caching for fast performance\nKey Features:\nMarket Movers Section: Displays live updates of Nifty, Sensex, and other indices, with top gainers and losers.\nInteractive Candlestick Charts: Custom-built charting using Plotly.js with support for indicators, zoom, pan, and crosshair tracking.\nBackend Caching: Implemented smart caching in FastAPI for high-speed data retrieval and reduced API calls.\nResponsive UI: Built with styled-components for seamless experience across devices."
        },
        {
            "name": "Best SMA combo",
            "description": "SMA Combo is a stock analysis tool for testing short\nlong SMA combinations across stocks and timeframes. It oﬀers backtesting, performance metrics, and\nvisualization , helping traders optimize strategies eciently\nFeatures :- Custom SMA selection, multi-timeframe backtesting, performance metrics, chart visualization, and fast data\ncaching."
        }
    ],
    "certifications": [],
    "domain_of_interest": [],
    "languages_known": [],
    "achievements": [
        "Awarded `Best Boy of the Year` for leadership achievements.",
        "Achieved 1200+ rating on Chess.com (Blitz Rapid formats).",
        "Built Rs.6.2L+ stock portfolio using self-developed analysis tools."
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Aspiring Algo Trader & Frontend Developer with real startup internships under my belt. Built production-level tools, live trading apps, and crushed 75+ LeetCode problems.",
    "personal_projects": [],
    "social_media": [
        "https://github.com/",
        "https://www.linkedin.com/"
    ]
}
```
----------------------------------------

================================================================================