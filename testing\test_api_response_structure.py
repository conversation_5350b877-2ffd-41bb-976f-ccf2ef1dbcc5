#!/usr/bin/env python3
"""
Simple test to show the clean API response structure
"""

import sys
import os
import json

# Add the parent directory to the path so we can import from main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import calculate_candidate_job_fit_new

def main():
    """Show the clean API response structure"""
    print("🔍 API Response Structure Test")
    print("=" * 50)
    
    # Sample data
    resume_data = {
        "name": "John Doe",
        "skills": ["Python", "JavaScript", "SQL"],
        "experience": [
            {"company": "TechCorp", "position": "Developer", "duration": "2020 - Present"}
        ],
        "education": [
            {"degree": "Bachelor of Science in Computer Science", "year": "2020"}
        ]
    }
    
    jd_data = {
        "title": "Software Engineer",
        "required_skills": ["Python", "JavaScript"],
        "preferred_skills": ["SQL", "React"],
        "education_requirements": ["Bachelor's degree in Computer Science"]
    }
    
    # Calculate CGPA
    result = calculate_candidate_job_fit_new(resume_data, jd_data)
    
    # Convert to JSON to see the structure
    result_json = result.model_dump()
    
    print("📊 Clean API Response Structure:")
    print(json.dumps(result_json, indent=2))
    
    print("\n🔍 Field Score Structure Analysis:")
    for field_name in ["skills_score", "experience_score", "education_score", "certifications_score", "location_score", "reliability_score"]:
        field_data = result_json[field_name]
        print(f"\n{field_name}:")
        for key in field_data.keys():
            print(f"  - {key}: {type(field_data[key]).__name__}")
        
        # Check if details attribute exists
        if "details" in field_data:
            print(f"  ❌ Contains 'details' attribute (should be removed)")
        else:
            print(f"  ✅ No 'details' attribute (clean)")
    
    print(f"\n📈 Summary:")
    print(f"✅ Total Score: {result.total_score:.2f}/10")
    print(f"✅ Fit Category: {result.fit_category}")
    print(f"✅ All field scores are clean (no calculation details)")
    print(f"✅ User-friendly rationales provided")
    print(f"✅ No technical jargon in API response")

if __name__ == "__main__":
    main()
