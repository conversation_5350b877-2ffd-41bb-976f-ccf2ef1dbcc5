
DETAILED CALCULATION BREAKDOWN
==============================
This file provides step-by-step explanations of how each score was calculated.

SKILLS SCORING CALCULATION
==========================
Formula: Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))
Explanation: Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)

Step-by-step calculation:
  Step 1: Required skills score = 6/18 × 8 = 2.67
  Step 2: No preferred skills specified, bonus = 0.00
  Step 3: Total score = min(10, 2.67 + 0.00) = 2.67


EXPERIENCE SCORING CALCULATION
==============================
Formula: Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x
Explanation: Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification

Step-by-step calculation:
  Step 1: Required experience extracted: 4 years from '2 to 4 years'
  Step 2: Analyzing 1 experience entries
    Entry 1: Unknown Company - Unknown Position (Present) = 0 years (could not parse)
  Step 2 Result: Total candidate experience = 0 years
  Step 3: Calculating experience score
    ✗ Could not determine candidate experience: Score = 5.0 (neutral)


EDUCATION SCORING CALCULATION
=============================
Formula: Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements
Explanation: Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)

Step-by-step calculation:
  Step 1: Checking 1 education requirement(s)
    - Analyzing requirement: 'Complete understanding of application development life cycle.'
      Required degree type: master
      Required field: Not specified
      Candidate degree: 'Bachelor of Technology (AI ML)' (Type: bachelor, Field: computer science)
      Candidate degree: 'Intermediate' (Type: master, Field: Unknown)
      ✓ EXACT MATCH FOUND: Degree type and field match
  Step 2: Applying binary scoring system
    ✓ Education requirement met: Score = 10.0


CERTIFICATIONS SCORING CALCULATION
==================================
Formula: Score = min(10, relevant_certifications_count × 2)
Explanation: Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10

Step-by-step calculation:
  Step 1: Found 1 certifications in resume
  Step 2: Checking relevance against 18 job skills (18 required + 0 preferred)
    Cert 1: 'Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functional gesture-controlled wheelchair prototype.' - NOT RELEVANT = +0 points
  Step 3: Score calculation
    Base score: 0 relevant certs × 2 points = 0
    Final score: min(10, 0) = 0


LOCATION SCORING CALCULATION
============================
Formula: 10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data
Explanation: Location scoring prioritizes current location match, gives credit for previous work experience in the job location

Step-by-step calculation:
  Step 1: Location extraction
    Job location: '' (from JD)
    Resume location: '' (from resume)
    Experience locations: [] (from work history)
  Step 2: Location matching analysis
    ~ Insufficient location data: Score = 5.0 (neutral)


RELIABILITY SCORING CALCULATION
===============================
Formula: Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year
Explanation: Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history

Step-by-step calculation:
  Step 1: Analyzing 1 experience entries for tenure calculation
    Entry 1: Unknown Company (Present) = 0 years (could not parse)
  Step 1 Result: Total experience = 0 years
  Step 2: Calculating job stability/reliability
    Total companies: 1
    Total years: 0
    ~ Insufficient data for calculation: Score = 5.0 (neutral)


FINAL CGPA CALCULATION
=====================
Formula: Final Score = (Sum of Weighted Scores) / (Sum of Weights)

Calculation:
  Skills: 2.67 × 4.0 = 10.67
  Experience: 5.00 × 3.0 = 15.00
  Education: 10.00 × 2.0 = 20.00
  Certifications: 0.00 × 0.5 = 0.00
  Location: 5.00 × 0.5 = 2.50
  Reliability: 5.00 × 0.0 = 0.00

  Total Weighted Score: 10.67 + 15.00 + 20.00 + 0.00 + 2.50 + 0.00 = 48.17
  Total Credits: 10.0

  Final Score: 48.17 / 10.0 = 4.82/10

CONCLUSION
==========
The candidate scored 4.82/10, which categorizes them as a "Moderate Match".
