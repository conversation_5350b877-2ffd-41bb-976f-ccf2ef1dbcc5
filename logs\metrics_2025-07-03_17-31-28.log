{"event": "session_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "timestamp": "2025-07-03T17:31:28.508607", "message": "New API session started"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "c1486da2-f9b7-485e-a640-e55ef7704fcf", "endpoint": "/", "timestamp": "2025-07-03T17:31:29.821983", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "c1486da2-f9b7-485e-a640-e55ef7704fcf", "endpoint": "/", "timestamp": "2025-07-03T17:31:29.823983", "total_time_seconds": 0.0020003318786621094, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "840971b5-ea79-49cf-b498-245ba0dc1f6f", "endpoint": "/favicon.ico", "timestamp": "2025-07-03T17:31:29.927592", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "840971b5-ea79-49cf-b498-245ba0dc1f6f", "endpoint": "/favicon.ico", "timestamp": "2025-07-03T17:31:29.928588", "total_time_seconds": 0.0009965896606445312, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "10b0c6af-9f78-423a-98c8-5a07a1506833", "endpoint": "/docs", "timestamp": "2025-07-03T17:31:32.207285", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "10b0c6af-9f78-423a-98c8-5a07a1506833", "endpoint": "/docs", "timestamp": "2025-07-03T17:31:32.208286", "total_time_seconds": 0.0010013580322265625, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "43197422-f639-467f-ac44-de60204d0120", "endpoint": "/openapi.json", "timestamp": "2025-07-03T17:31:32.333612", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "43197422-f639-467f-ac44-de60204d0120", "endpoint": "/openapi.json", "timestamp": "2025-07-03T17:31:32.360082", "total_time_seconds": 0.026470422744750977, "status_code": 200, "message": "Request completed in 0.0265s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:01.787791", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:01.832623", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:01.833635", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:01.835153", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:01.836163", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:01.841172", "file_processing_time": 0.0393071174621582, "message": "Custom metric: file_processing_time=0.0393071174621582"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8eed0fbb-e760-4a61-acbb-ec3bd4c26457", "endpoint": "/hybrid_resume", "timestamp": "2025-07-03T17:32:33.018186", "total_time_seconds": 31.230395317077637, "status_code": 200, "message": "Request completed in 31.2304s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8c9cacef-7ce6-4d11-8a60-bbe5060aa31e", "endpoint": "/jd_parser", "timestamp": "2025-07-03T17:32:33.019419", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "8c9cacef-7ce6-4d11-8a60-bbe5060aa31e", "endpoint": "/jd_parser", "timestamp": "2025-07-03T17:32:42.734294", "total_time_seconds": 9.71487545967102, "status_code": 200, "message": "Request completed in 9.7149s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:07.992337", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:07.997853", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:08.202144", "final_score": 5.67, "message": "Custom metric: final_score=5.67"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:08.202144", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:08.202144", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:08.203653", "log_folder": "intervet_new_logs\\intervet_new_20250703_173308_057_1751544188057", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250703_173308_057_1751544188057"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "e01938b5-08a2-4481-b908-b667ae6d9c5a", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:08.205661", "total_time_seconds": 0.21332478523254395, "status_code": 200, "message": "Request completed in 0.2133s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.560047", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.561044", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.572251", "final_score": 5.968421052631579, "message": "Custom metric: final_score=5.968421052631579"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.573252", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.573760", "total_credits_used": 9.5, "message": "Custom metric: total_credits_used=9.5"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.573760", "log_folder": "intervet_new_logs\\intervet_new_20250703_173341_563_1751544221563", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250703_173341_563_1751544221563"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "53874e67-2272-4354-b5e8-7877537c334c", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:33:41.574768", "total_time_seconds": 0.014720916748046875, "status_code": 200, "message": "Request completed in 0.0147s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.929309", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.930309", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.940379", "final_score": 5.67, "message": "Custom metric: final_score=5.67"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.941375", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.941375", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.941375", "log_folder": "intervet_new_logs\\intervet_new_20250703_173400_932_1751544240932", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250703_173400_932_1751544240932"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "1a3baad7-713f-468e-bee3-365cb509348e", "endpoint": "/intervet_new", "timestamp": "2025-07-03T17:34:00.941375", "total_time_seconds": 0.012066125869750977, "status_code": 200, "message": "Request completed in 0.0121s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.008528", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.018057", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.029075", "final_score": 5.968421052631579, "message": "Custom metric: final_score=5.968421052631579"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.029075", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.029075", "total_credits_used": 9.5, "message": "Custom metric: total_credits_used=9.5"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.029075", "log_folder": "intervet_new_logs\\intervet_new_20250703_183058_021_1751547658021", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250703_183058_021_1751547658021"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "19a1bf4d-17b6-476c-bb0a-51d95e619106", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:30:58.030079", "total_time_seconds": 0.02155017852783203, "status_code": 200, "message": "Request completed in 0.0216s with status 200"}
{"event": "request_start", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.860258", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.860258", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.869805", "final_score": 6.022222222222222, "message": "Custom metric: final_score=6.022222222222222"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.869805", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.869805", "total_credits_used": 9.0, "message": "Custom metric: total_credits_used=9.0"}
{"event": "custom_metric", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.869805", "log_folder": "intervet_new_logs\\intervet_new_20250703_183139_863_1751547699863", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250703_183139_863_1751547699863"}
{"event": "request_complete", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "request_id": "611731e1-492f-4181-81c4-6a63e8d333dc", "endpoint": "/intervet_new", "timestamp": "2025-07-03T18:31:39.871312", "total_time_seconds": 0.011054277420043945, "status_code": 200, "message": "Request completed in 0.0111s with status 200"}
{"event": "session_end", "session_id": "4cc251f1-ca5f-415b-93c3-83eafb303524", "timestamp": "2025-07-03T18:49:14.739970", "message": "API session ended"}
