{"event": "session_start", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "timestamp": "2025-07-08T13:16:31.246481", "message": "New API session started"}
{"event": "request_start", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:33.254756", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:33.271756", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:33.272752", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:33.272752", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:33.272752", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:33.272752", "file_processing_time": 0.014002084732055664, "message": "Custom metric: file_processing_time=0.014002084732055664"}
{"event": "request_complete", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "4a106113-e420-490b-b023-7fba2ba791de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:49.973289", "total_time_seconds": 16.718533277511597, "status_code": 200, "message": "Request completed in 16.7185s with status 200"}
{"event": "request_start", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:51.995356", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:52.008356", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:52.009355", "file_size_bytes": 81408, "message": "Custom metric: file_size_bytes=81408"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:52.009355", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:52.009355", "extracted_text_length": 2452, "message": "Custom metric: extracted_text_length=2452"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:16:52.009355", "file_processing_time": 0.011004209518432617, "message": "Custom metric: file_processing_time=0.011004209518432617"}
{"event": "request_complete", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "17ecbe97-3fdc-41a5-8508-0b954097d1e1", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:17:06.473383", "total_time_seconds": 14.478027105331421, "status_code": 200, "message": "Request completed in 14.4780s with status 200"}
{"event": "request_start", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.504365", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.505365", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.511366", "final_score": 6.04, "message": "Custom metric: final_score=6.04"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.511366", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.511366", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.511366", "log_folder": "intervet_new_logs\\intervet_new_20250708_131708_507_1751960828507", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_131708_507_1751960828507"}
{"event": "request_complete", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "request_id": "b19181e6-6766-472a-9740-ed0c044f56d5", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:17:08.512368", "total_time_seconds": 0.008002996444702148, "status_code": 200, "message": "Request completed in 0.0080s with status 200"}
{"event": "session_end", "session_id": "f8a8d136-041f-4735-8f89-cf2c443cd292", "timestamp": "2025-07-08T13:19:27.095171", "message": "API session ended"}
