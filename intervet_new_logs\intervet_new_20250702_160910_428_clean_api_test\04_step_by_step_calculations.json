{"timestamp": "20250702_160910_428", "calculation_transparency": "This file contains detailed step-by-step calculations for each scoring field", "field_calculations": {"skills": {"calculation_steps": ["Step 1: Required skills score = 3/3 × 8 = 8.00", "Step 2: Preferred skills bonus = 2/3 × 2 = 1.33", "Step 3: Total score = min(10, 8.00 + 1.33) = 9.33"], "scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)", "matching_details": []}, "experience": {"calculation_steps": ["Step 1: Required experience extracted: 2 years from '2+ years'", "Step 2: Analyzing 2 experience entries", "  Entry 1: Tech Solutions - Software Engineer (2020 - Present) = 4 years", "  Entry 2: StartupABC - Junior Developer (2018 - 2020) = 2 years", "Step 2 Result: Total candidate experience = 6 years", "Step 3: Calculating experience score", "  Experience ratio: 6/2 = 3.00", "  ~ Significantly over-experienced (>2.5): Score = 6.0"], "scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification", "experience_breakdown": [{"company": "Tech Solutions", "position": "Software Engineer", "duration": "2020 - Present", "years_calculated": 4, "calculation_method": "Year range: 2020 to 2024"}, {"company": "StartupABC", "position": "Junior Developer", "duration": "2018 - 2020", "years_calculated": 2, "calculation_method": "Year range: 2018 to 2020"}]}, "education": {"calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'Bachelor's degree in Computer Science'", "    Required degree type: bachelor", "    Required field: computer science", "    Candidate degree: 'Bachelor of Science in Computer Science' (Type: bachelor, Field: computer science)", "    ✓ EXACT MATCH FOUND: Degree type and field match", "Step 2: Applying binary scoring system", "  ✓ Education requirement met: Score = 10.0"], "scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)", "match_type": "exact_match"}, "certifications": {"calculation_steps": ["Step 1: Found 1 certifications in resume", "Step 2: Checking relevance against 6 job skills (3 required + 3 preferred)", "  Cert 1: 'AWS Certified Developer' - RELEVANT (matches: AWS) = +2 points", "Step 3: Score calculation", "  Base score: 1 relevant certs × 2 points = 2", "  Final score: min(10, 2) = 2"], "scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10", "certification_analysis": [{"certification": "AWS Certified Developer", "relevant": true, "matched_skills": ["AWS"], "points_awarded": 2}]}, "location": {"calculation_steps": ["Step 1: Location extraction", "  Job location: 'new york, ny' (from JD)", "  Resume location: 'new york, ny' (from resume)", "  Experience locations: ['new york, ny', 'new york, ny'] (from work history)", "Step 2: Location matching analysis", "  ✓ Current location match: Score = 10.0"], "scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}, "reliability": {"calculation_steps": ["Step 1: Analyzing 2 experience entries for tenure calculation", "  Entry 1: Tech Solutions (2020 - Present) = 4 years", "  Entry 2: StartupABC (2018 - 2020) = 2 years", "Step 1 Result: Total experience = 6 years", "Step 2: Calculating job stability/reliability", "  Total companies: 2", "  Total years: 6", "  Average tenure: 6/2 = 3.00 years per company", "  ✓ Very good stability (3-4 years): Score = 8.5"], "scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history", "tenure_breakdown": [{"company": "Tech Solutions", "duration": "2020 - Present", "years_calculated": 4, "calculation_method": "Year range: 2020 to 2024"}, {"company": "StartupABC", "duration": "2018 - 2020", "years_calculated": 2, "calculation_method": "Year range: 2018 to 2020"}]}}, "final_calculation": {"formula": "Final Score = (Sum of Weighted Scores) / (Sum of Weights)", "calculation": "(28.00 + 15.00 + 20.00 + 2.00 + 10.00 + 4.25) / 10.0", "result": "7.92/10"}}