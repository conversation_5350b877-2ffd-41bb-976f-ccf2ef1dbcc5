{"total_score": 4.816666666666666, "fit_category": "Moderate Match", "summary": "The candidate is a moderate match for this position with a CGPA-style score of 4.8/10. Key strengths: Education. Areas for improvement: Skills, Certifications.", "skills_score": {"raw_score": 2.6666666666666665, "weight": 4.0, "weighted_score": 10.666666666666666, "rationale": "Limited skills match - has 6/18 required skills, major skill development needed"}, "experience_score": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps"}, "education_score": {"raw_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Perfect education match - meets all degree requirements"}, "certifications_score": {"raw_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No relevant certifications - 1 certifications but none match job requirements"}, "location_score": {"raw_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Moderate location match - some geographic alignment"}, "reliability_score": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company"}, "detailed_rationale": {"skills_match_direct": "Matched 6/18 required skills. Matched required skills: S<PERSON>, Microsoft SQL Database Platform, P L/SQL, HTML, CSS, JavaScript. Missing required skills: C#, VB.NET, .NET, WCF, WF, ASP.NET, Oracle Databases, Silverlight, AJAX, MVC, JQuery, XML", "experience_match": "Below requirements: 0 years vs required 4 years (ratio: 0.00)", "reliability": "Poor stability: frequent job changes with 0.0 years per company", "location_match": "Location information not available for comparison", "academic_match": "Education requirements met: 'Intermediate' matches requirement 'Complete understanding of application development life cycle.'", "alma_mater": "No top-ranked universities found in education history", "certifications": "No relevant certifications found"}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.006000041961669922}