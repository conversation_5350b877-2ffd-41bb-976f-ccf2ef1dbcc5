{"event": "session_start", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "timestamp": "2025-07-08T13:34:40.055831", "message": "New API session started"}
{"event": "request_start", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:41.030797", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:41.047798", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:41.047798", "file_size_bytes": 81408, "message": "Custom metric: file_size_bytes=81408"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:41.047798", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:41.047798", "extracted_text_length": 2452, "message": "Custom metric: extracted_text_length=2452"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:41.047798", "file_processing_time": 0.013998985290527344, "message": "Custom metric: file_processing_time=0.013998985290527344"}
{"event": "request_complete", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "7d6b7b81-92ce-492a-ba0e-1a66d8d7a4de", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:25.290789", "total_time_seconds": 44.259992599487305, "status_code": 200, "message": "Request completed in 44.2600s with status 200"}
{"event": "request_start", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:27.328213", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:27.341722", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:27.341722", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:27.341722", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:27.341722", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:35:27.341722", "file_processing_time": 0.011505365371704102, "message": "Custom metric: file_processing_time=0.011505365371704102"}
{"event": "request_complete", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "62cec815-f592-4365-b3b2-8743c24ce923", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:36:09.658135", "total_time_seconds": 42.32992172241211, "status_code": 200, "message": "Request completed in 42.3299s with status 200"}
{"event": "request_start", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.702192", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.702192", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.709701", "final_score": 5.6, "message": "Custom metric: final_score=5.6"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.709701", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.709701", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.709701", "log_folder": "intervet_new_logs\\intervet_new_20250708_133611_704_1751961971704", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_133611_704_1751961971704"}
{"event": "request_complete", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "6491072b-1807-402a-9fbe-f6f46c6fbaca", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:11.710703", "total_time_seconds": 0.008511066436767578, "status_code": 200, "message": "Request completed in 0.0085s with status 200"}
{"event": "request_start", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.760587", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.760587", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.768119", "final_score": 6.04, "message": "Custom metric: final_score=6.04"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.769125", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.769125", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.769125", "log_folder": "intervet_new_logs\\intervet_new_20250708_133613_762_1751961973762", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_133613_762_1751961973762"}
{"event": "request_complete", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "request_id": "482c2232-7d0f-4a4c-8bf0-a4b657fc6e29", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:36:13.769125", "total_time_seconds": 0.008538007736206055, "status_code": 200, "message": "Request completed in 0.0085s with status 200"}
{"event": "session_end", "session_id": "692b039c-7af0-41e9-85c6-5644943ca799", "timestamp": "2025-07-08T13:37:40.204413", "message": "API session ended"}
