{"event": "session_start", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "timestamp": "2025-07-02T16:02:47.931076", "message": "New API session started"}
{"event": "request_start", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "03e46950-d223-4a41-964c-9d4cbc3e411d", "endpoint": "/", "timestamp": "2025-07-02T16:02:49.165220", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "03e46950-d223-4a41-964c-9d4cbc3e411d", "endpoint": "/", "timestamp": "2025-07-02T16:02:49.166220", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "020c944f-4f46-45c9-bbee-40a564ca7f50", "endpoint": "/docs", "timestamp": "2025-07-02T16:02:52.034861", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "020c944f-4f46-45c9-bbee-40a564ca7f50", "endpoint": "/docs", "timestamp": "2025-07-02T16:02:52.034861", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "5a32890c-e25c-4324-84f9-382a035ebfb8", "endpoint": "/openapi.json", "timestamp": "2025-07-02T16:02:52.102873", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "5a32890c-e25c-4324-84f9-382a035ebfb8", "endpoint": "/openapi.json", "timestamp": "2025-07-02T16:02:52.116382", "total_time_seconds": 0.013509750366210938, "status_code": 200, "message": "Request completed in 0.0135s with status 200"}
{"event": "request_start", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.235295", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.236302", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.249293", "final_score": 5.839160839160839, "message": "Custom metric: final_score=5.839160839160839"}
{"event": "custom_metric", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.250295", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.250295", "total_credits_used": 13.0, "message": "Custom metric: total_credits_used=13.0"}
{"event": "custom_metric", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.250295", "log_folder": "intervet_new_logs\\intervet_new_20250702_160258_243_1751452378243", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_160258_243_1751452378243"}
{"event": "request_complete", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "request_id": "70b4a051-7c61-4491-a5c3-0ff4b00ff1e6", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:58.251301", "total_time_seconds": 0.016005516052246094, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
{"event": "session_end", "session_id": "7b6d085d-c386-4a82-a1cd-4dfcfa7af2d2", "timestamp": "2025-07-02T16:07:47.527509", "message": "API session ended"}
