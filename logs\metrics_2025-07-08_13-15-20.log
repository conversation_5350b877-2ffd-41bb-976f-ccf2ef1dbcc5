{"event": "session_start", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "timestamp": "2025-07-08T13:15:20.588435", "message": "New API session started"}
{"event": "request_start", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:22.907153", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:22.923151", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:22.923151", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:22.923151", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:22.924154", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:22.924154", "file_processing_time": 0.012999296188354492, "message": "Custom metric: file_processing_time=0.012999296188354492"}
{"event": "request_complete", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "f21aa73e-80f4-4748-a697-574baf5da78d", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:15:43.057693", "total_time_seconds": 20.15053939819336, "status_code": 200, "message": "Request completed in 20.1505s with status 200"}
{"event": "request_start", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.082866", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.082866", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.091869", "final_score": 6.04, "message": "Custom metric: final_score=6.04"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.091869", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.091869", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.091869", "log_folder": "intervet_new_logs\\intervet_new_20250708_131545_085_1751960745085", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_131545_085_1751960745085"}
{"event": "request_complete", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "request_id": "52207cc5-f699-4e4c-b1c3-508fa5e90193", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:15:45.092868", "total_time_seconds": 0.010001897811889648, "status_code": 200, "message": "Request completed in 0.0100s with status 200"}
{"event": "session_end", "session_id": "71595800-70d8-48d4-a207-5bd23deafe2c", "timestamp": "2025-07-08T13:16:30.329304", "message": "API session ended"}
