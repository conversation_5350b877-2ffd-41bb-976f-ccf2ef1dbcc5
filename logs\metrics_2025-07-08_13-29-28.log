{"event": "session_start", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "timestamp": "2025-07-08T13:29:28.875502", "message": "New API session started"}
{"event": "request_start", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:07.551430", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:07.567435", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:07.567435", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:07.567435", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:07.567435", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:07.568435", "file_processing_time": 0.01300358772277832, "message": "Custom metric: file_processing_time=0.01300358772277832"}
{"event": "request_complete", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "request_id": "e83b1dbe-02e6-4dd3-8b33-22f2cee02561", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:34:39.000993", "total_time_seconds": 31.449562788009644, "status_code": 200, "message": "Request completed in 31.4496s with status 200"}
{"event": "session_end", "session_id": "7e0836dc-90da-46c4-9dd2-583e0b6d63b8", "timestamp": "2025-07-08T13:34:39.113030", "message": "API session ended"}
