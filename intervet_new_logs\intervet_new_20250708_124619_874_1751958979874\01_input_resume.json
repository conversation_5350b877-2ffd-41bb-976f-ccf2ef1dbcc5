{"name": "<PERSON><PERSON>", "email": null, "phone": null, "education": [{"degree": "Bachelor of Technology (Artificial intelligence)", "institution": "Newton school of Technology, Rishihood University", "year": "2023 - 2027"}, {"degree": "Intermediate (Class XII)", "institution": "DELHI PUBLIC SCHOOL, HISAR", "year": "2021 - 2022"}, {"degree": "Matriculation (Class X)", "institution": "DELHI PUBLIC SCHOOL, HISAR", "year": "2019 - 2020"}], "skills": ["C++", "Python", "JavaScript", "CSS", "HTML", "Java", "React", "Express JS", "Prisma ORM", "NodeJS", "MongoDB", "React", "CSS", "JavaScript", "Tailwind", "HTML", "Bootstrap", "Data Structure"], "experience": [{"company_name": null, "role": null, "duration": null, "key_responsibilities": null}], "projects": [{"name": "Theta: The Learning App", "description": "Theta is a cutting-edge E-learning platform designed to provide personalized, engaging educational content. \n\nTailored for diverse learners, Theta makes learning accessible and interactive anytime, anywhere.\n\n\nFeatures:\n\n\nAs an admin user can perform CRUD operations to create Courses, Lectures and manage costs of it.\n\n\nAs a buyer user can buy course to watch lectures and learn.\n\n\nAs a superadmin user can manage roles and permissions of admin and user.\n\n\nRazorpay APIs also have been integrated.\n\n\nUsed react-router-dom for routing and pagination for interactive and immersive user experience and can create and log\ninstitute and log into their respective accounts securely."}, {"name": "The Souled Store Website Clone", "description": "Developed a replica of The Souled Store using React, demonstrating front-end proficiency and ensuring a seamless user experience across various devices.\n\n\nFeatures:\n\n\nImplemented modular React components for eficient UI management.\n\n\nManaged application state effectively across components.\n\n\nSet up routing for seamless navigation between pages.\n\n\nUtilized Context API for global state management.\n\n\nEnsured a fully responsive, mobile-first design."}, {"name": "Art Gallery Website", "description": "Developed a dynamic online Art Gallery platform connecting art enthusiasts, collectors, and creators worldwide, offering an engaging and user-friendly experience.\n\n\nFeatures:\n\n\nCurated a diverse collection of artworks from both established and emerging artists across various mediums.\n\n\nEnabled users to discover, buy, and sell art in a seamless marketplace.\n\n\nFostered a vibrant community for expanding collections and showcasing unique creations."}], "certifications": [], "domain_of_interest": [], "languages_known": [], "achievements": [], "publications": [], "volunteer_experience": [], "references": [], "summary": null, "personal_projects": [], "social_media": [], "processing_time": 68.**************, "extraction_method": "hybrid_regex_llm", "sections_extracted": 6, "regex_confidence": 0.9166666666666666}