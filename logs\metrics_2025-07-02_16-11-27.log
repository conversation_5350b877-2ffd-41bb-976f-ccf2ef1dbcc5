{"event": "session_start", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "timestamp": "2025-07-02T16:11:27.844304", "message": "New API session started"}
{"event": "request_start", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "18a89188-4fab-4c68-9bea-063b6c2c9440", "endpoint": "/", "timestamp": "2025-07-02T16:11:35.775696", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "18a89188-4fab-4c68-9bea-063b6c2c9440", "endpoint": "/", "timestamp": "2025-07-02T16:11:35.779706", "total_time_seconds": 0.004010677337646484, "status_code": 200, "message": "Request completed in 0.0040s with status 200"}
{"event": "request_start", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "1d3fc274-a079-4453-b2e3-fec2862d8594", "endpoint": "/docs", "timestamp": "2025-07-02T16:11:37.875726", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "1d3fc274-a079-4453-b2e3-fec2862d8594", "endpoint": "/docs", "timestamp": "2025-07-02T16:11:37.876724", "total_time_seconds": 0.0009980201721191406, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "ffbe29ed-91ec-4000-ba88-89b0663bb033", "endpoint": "/openapi.json", "timestamp": "2025-07-02T16:11:37.946255", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "ffbe29ed-91ec-4000-ba88-89b0663bb033", "endpoint": "/openapi.json", "timestamp": "2025-07-02T16:11:37.961254", "total_time_seconds": 0.014999151229858398, "status_code": 200, "message": "Request completed in 0.0150s with status 200"}
{"event": "request_start", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.908932", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.909934", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.940931", "final_score": 5.839160839160839, "message": "Custom metric: final_score=5.839160839160839"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.940931", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.940931", "total_credits_used": 13.0, "message": "Custom metric: total_credits_used=13.0"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.940931", "log_folder": "intervet_new_logs\\intervet_new_20250702_161146_917_1751452906917", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_161146_917_1751452906917"}
{"event": "request_complete", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "cee17dee-643e-4fb8-88c9-7834e21781af", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:11:46.941932", "total_time_seconds": 0.032999515533447266, "status_code": 200, "message": "Request completed in 0.0330s with status 200"}
{"event": "request_start", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.185083", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.187078", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.207264", "final_score": 4.893939393939394, "message": "Custom metric: final_score=4.893939393939394"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.208773", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.208773", "total_credits_used": 6.0, "message": "Custom metric: total_credits_used=6.0"}
{"event": "custom_metric", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.208773", "log_folder": "intervet_new_logs\\intervet_new_20250702_161254_194_1751452974194", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_161254_194_1751452974194"}
{"event": "request_complete", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "request_id": "23596fdf-fadf-4e49-b758-35ad9090419d", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:12:54.209782", "total_time_seconds": 0.02469921112060547, "status_code": 200, "message": "Request completed in 0.0247s with status 200"}
{"event": "session_end", "session_id": "663b4e22-3140-4357-aa39-05d947f8356b", "timestamp": "2025-07-02T16:13:25.055418", "message": "API session ended"}
