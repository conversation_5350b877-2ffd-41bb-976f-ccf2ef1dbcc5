#!/usr/bin/env python3
"""
Test to verify that the API response is clean (no calculation steps) 
but backend logging still contains detailed calculations.
"""

import sys
import os
import json
from pathlib import Path

# Add the parent directory to the path so we can import from main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import (
    WeightageConfig, 
    calculate_candidate_job_fit_new,
    log_intervet_new_calculation
)

def get_test_data():
    """Get test resume and JD data"""
    resume_data = {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "location": "New York, NY",
        "skills": ["Python", "JavaScript", "React", "SQL", "AWS"],
        "experience": [
            {
                "company": "Tech Solutions",
                "position": "Software Engineer",
                "duration": "2020 - Present",
                "location": "New York, NY"
            },
            {
                "company": "StartupABC",
                "position": "Junior Developer", 
                "duration": "2018 - 2020",
                "location": "New York, NY"
            }
        ],
        "education": [
            {
                "degree": "Bachelor of Science in Computer Science",
                "institution": "NYU",
                "year": "2018"
            }
        ],
        "certifications": [
            {
                "name": "AWS Certified Developer",
                "year": "2021"
            }
        ]
    }
    
    jd_data = {
        "title": "Senior Software Engineer",
        "company": "TechCorp",
        "location": "New York, NY",
        "required_skills": ["Python", "JavaScript", "SQL"],
        "preferred_skills": ["React", "AWS", "Docker"],
        "required_experience": "2+ years",
        "education_requirements": ["Bachelor's degree in Computer Science"]
    }
    
    return resume_data, jd_data

def test_clean_api_response():
    """Test that API response doesn't contain calculation steps"""
    print("🧪 Testing Clean API Response")
    print("=" * 50)
    
    resume_data, jd_data = get_test_data()
    
    # Calculate CGPA
    result = calculate_candidate_job_fit_new(resume_data, jd_data)
    
    print(f"✅ CGPA calculation completed")
    print(f"📊 Total Score: {result.total_score:.2f}/10")
    print(f"🎯 Fit Category: {result.fit_category}")
    
    # Check that API response doesn't contain calculation steps
    print(f"\n🔍 Checking API Response Cleanliness:")
    
    fields_to_check = [
        ("Skills", result.skills_score),
        ("Experience", result.experience_score),
        ("Education", result.education_score),
        ("Certifications", result.certifications_score),
        ("Location", result.location_score),
        ("Reliability", result.reliability_score)
    ]
    
    all_clean = True
    
    for field_name, field_score in fields_to_check:
        # Check that the field score doesn't have details attribute (clean API)
        has_details = hasattr(field_score, 'details')

        if has_details:
            print(f"   ❌ {field_name}: Still contains details attribute")
            all_clean = False
        else:
            print(f"   ✅ {field_name}: Clean API response (no details attribute)")

        # Check rationale is user-friendly
        rationale = field_score.rationale
        if any(word in rationale.lower() for word in ["calculation", "formula", "step", "ratio"]):
            print(f"   ⚠️  {field_name}: Rationale may be too technical: '{rationale[:50]}...'")
        else:
            print(f"   ✅ {field_name}: User-friendly rationale")
    
    # Check that normalized_score is removed
    print(f"\n🔍 Checking normalized_score Removal:")
    for field_name, field_score in fields_to_check:
        if hasattr(field_score, 'normalized_score'):
            print(f"   ❌ {field_name}: Still has normalized_score field")
            all_clean = False
        else:
            print(f"   ✅ {field_name}: normalized_score removed")
    
    return all_clean

def test_backend_logging_completeness():
    """Test that backend logging still contains all calculation details"""
    print(f"\n🧪 Testing Backend Logging Completeness")
    print("=" * 50)
    
    resume_data, jd_data = get_test_data()
    weights = WeightageConfig()
    
    # Calculate CGPA
    result = calculate_candidate_job_fit_new(resume_data, jd_data, weights)
    
    # Create logs
    log_folder = log_intervet_new_calculation(resume_data, jd_data, weights, result, "clean_api_test")
    
    if not log_folder:
        print("❌ Failed to create logs")
        return False
    
    print(f"✅ Logs created in: {log_folder}")
    
    # Check that step-by-step calculations file exists and contains data
    step_by_step_file = os.path.join(log_folder, "04_step_by_step_calculations.json")
    if os.path.exists(step_by_step_file):
        print(f"✅ Step-by-step calculations file exists")
        
        with open(step_by_step_file, 'r', encoding='utf-8') as f:
            step_data = json.load(f)
        
        # Check that all fields have calculation steps
        field_calculations = step_data.get("field_calculations", {})
        for field_name in ["skills", "experience", "education", "certifications", "location", "reliability"]:
            field_calc = field_calculations.get(field_name, {})
            calc_steps = field_calc.get("calculation_steps", [])
            
            if calc_steps:
                print(f"   ✅ {field_name.title()}: {len(calc_steps)} calculation steps logged")
            else:
                print(f"   ❌ {field_name.title()}: No calculation steps found")
    else:
        print(f"❌ Step-by-step calculations file missing")
        return False
    
    # Check that detailed explanation file exists
    detailed_file = os.path.join(log_folder, "07_detailed_calculation_explanation.txt")
    if os.path.exists(detailed_file):
        print(f"✅ Detailed calculation explanation file exists")
        
        with open(detailed_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that it contains calculation details
        if "Step-by-step calculation:" in content and "Formula:" in content:
            print(f"   ✅ Contains detailed calculation explanations")
        else:
            print(f"   ❌ Missing detailed calculation explanations")
    else:
        print(f"❌ Detailed calculation explanation file missing")
        return False
    
    return True

def test_user_friendly_rationales():
    """Test that rationales are user-friendly for HR"""
    print(f"\n🧪 Testing User-Friendly Rationales")
    print("=" * 50)
    
    resume_data, jd_data = get_test_data()
    
    # Calculate CGPA
    result = calculate_candidate_job_fit_new(resume_data, jd_data)
    
    fields_to_check = [
        ("Skills", result.skills_score),
        ("Experience", result.experience_score),
        ("Education", result.education_score),
        ("Certifications", result.certifications_score),
        ("Location", result.location_score),
        ("Reliability", result.reliability_score)
    ]
    
    print("📝 User-Friendly Rationales:")
    for field_name, field_score in fields_to_check:
        print(f"\n{field_name} ({field_score.raw_score:.1f}/10):")
        print(f"   {field_score.rationale}")
        
        # Check if rationale is user-friendly
        technical_words = ["calculation", "formula", "ratio", "normalized", "weighted", "algorithm"]
        is_technical = any(word in field_score.rationale.lower() for word in technical_words)
        
        if is_technical:
            print(f"   ⚠️  May be too technical for HR")
        else:
            print(f"   ✅ User-friendly for HR")
    
    return True

def main():
    """Run all clean API response tests"""
    print("🚀 Clean API Response Test Suite")
    print("=" * 70)
    print("Testing that API responses are clean while backend logging is complete")
    print("=" * 70)
    
    try:
        # Test 1: Clean API response
        clean_api = test_clean_api_response()
        
        # Test 2: Backend logging completeness
        complete_logging = test_backend_logging_completeness()
        
        # Test 3: User-friendly rationales
        friendly_rationales = test_user_friendly_rationales()
        
        print("\n" + "=" * 70)
        if clean_api and complete_logging and friendly_rationales:
            print("🎉 All Clean API Response Tests Passed!")
            print("✅ API responses are clean (no calculation steps)")
            print("✅ Backend logging is complete and detailed")
            print("✅ Rationales are user-friendly for HR")
            print("✅ normalized_score successfully removed")
        else:
            print("❌ Some tests failed")
            if not clean_api:
                print("   - API response cleanliness issues")
            if not complete_logging:
                print("   - Backend logging completeness issues")
            if not friendly_rationales:
                print("   - User-friendly rationale issues")
        print("=" * 70)
        
        return clean_api and complete_logging and friendly_rationales
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
