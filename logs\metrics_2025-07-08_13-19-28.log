{"event": "session_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "timestamp": "2025-07-08T13:19:28.661225", "message": "New API session started"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "b52231bd-e1e6-4207-a03e-c9190e14f05f", "endpoint": "/", "timestamp": "2025-07-08T13:19:42.563008", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "b52231bd-e1e6-4207-a03e-c9190e14f05f", "endpoint": "/", "timestamp": "2025-07-08T13:19:42.565012", "total_time_seconds": 0.002003908157348633, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "59a09899-4bc8-4b52-8ee2-6c06e3ee9517", "endpoint": "/docs", "timestamp": "2025-07-08T13:19:47.937703", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "59a09899-4bc8-4b52-8ee2-6c06e3ee9517", "endpoint": "/docs", "timestamp": "2025-07-08T13:19:47.938704", "total_time_seconds": 0.0010001659393310547, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "3adbf572-6e50-4015-a893-73e18c54470d", "endpoint": "/openapi.json", "timestamp": "2025-07-08T13:19:48.008728", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "3adbf572-6e50-4015-a893-73e18c54470d", "endpoint": "/openapi.json", "timestamp": "2025-07-08T13:19:48.027727", "total_time_seconds": 0.01899886131286621, "status_code": 200, "message": "Request completed in 0.0190s with status 200"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:06.236056", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:06.258057", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:06.258057", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:06.258057", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:06.259060", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:06.259060", "file_processing_time": 0.018001079559326172, "message": "Custom metric: file_processing_time=0.018001079559326172"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "77ef4e22-220e-4eb8-b30b-005f1de3bd9f", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:20:49.368785", "total_time_seconds": 43.132729053497314, "status_code": 200, "message": "Request completed in 43.1327s with status 200"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "2ba2b094-8f88-4f72-8d98-c5a6ed863055", "endpoint": "/jd_parser", "timestamp": "2025-07-08T13:20:49.369786", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "2ba2b094-8f88-4f72-8d98-c5a6ed863055", "endpoint": "/jd_parser", "timestamp": "2025-07-08T13:21:00.187663", "total_time_seconds": 10.817877769470215, "status_code": 200, "message": "Request completed in 10.8179s with status 200"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:29.487484", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:29.505061", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:29.505061", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:29.505061", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:29.505061", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:29.505061", "file_processing_time": 0.014509201049804688, "message": "Custom metric: file_processing_time=0.014509201049804688"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "11376f44-2c45-43c5-85c6-e89b854bf8ae", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:21:45.998954", "total_time_seconds": 16.51146960258484, "status_code": 200, "message": "Request completed in 16.5115s with status 200"}
{"event": "request_start", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.594439", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.596440", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.609441", "final_score": 4.816666666666666, "message": "Custom metric: final_score=4.816666666666666"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.609441", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.609441", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.610442", "log_folder": "intervet_new_logs\\intervet_new_20250708_132321_602_1751961201602", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_132321_602_1751961201602"}
{"event": "request_complete", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "request_id": "942360e7-293a-4a68-b2f9-7ea98e2ea24f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:23:21.610442", "total_time_seconds": 0.016002655029296875, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
{"event": "session_end", "session_id": "7f781f7b-d929-4aa1-b1ec-bf1f2f4abd01", "timestamp": "2025-07-08T13:29:28.304142", "message": "API session ended"}
