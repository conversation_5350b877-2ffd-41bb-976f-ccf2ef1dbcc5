{"name": "<PERSON><PERSON>", "email": null, "phone": null, "education": [{"degree": "Bachelor of Technology (AI ML)", "institution": "Newton School of Technology , Rishihood University", "year": "2023 - 2027", "grade": "9.18/10.0"}, {"degree": "Intermediate", "institution": "Mother India Sr Sec School Marot, Jhajjar , Haryana", "year": "2021 - 2022", "grade": "90.0%"}, {"degree": "Matriculation", "institution": "R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana", "year": "2019 - 2020", "grade": "95.8%"}], "highest_education": "Bachelor of Technology (AI ML)", "skills": {"SQL": "Mentioned in resume", "Java": "Used in project: Zee5 Clone", "JavaScript": "Used in project: Zee5 Clone", "CSS": "Used in project: Zee5 Clone", "HTML": "Used in project: Zee5 Clone", "Python": "Mentioned in resume", "React": "Used in project: Zee5 Clone", "MySQL": "Mentioned in resume", "NodeJS": "Mentioned in resume", "Prisma ORM": "Mentioned in resume", "Tailwind": "Mentioned in resume", "Data Structure": "Mentioned in resume", "Communication Skills": "Mentioned in resume", "Research": "Mentioned in resume", "Decision-making": "Mentioned in resume", "Team Building": "Mentioned in resume", "Leadership": "Mentioned in resume"}, "experience": [{"company_name": "Google Developer Groups (GDG) Rishihood University", "role": "Tech Lead", "duration": "Present", "key_responsibilities": "Led a team of developers in organizing tech events, facilitated workshops, and mentored junior members. Contributed to the growth of the GDG community."}], "projects": [{"name": "Tech Talks", "description": "Developed 'Tech Talks', a blogging site for tech content. Users can register, log in, create , access and comment on tech blogs . Tech Stack: React, Node.js, HTML /CSS. Feature: Authentication , blog management, categorized content ,CRUD operations on Blogs"}, {"name": "Zee5 Clone", "description": "Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access a library of movies with intuitive UI. Tech Stack: React, HTML /CSS andJavaScript. Feature : Movie rendering , Authentication."}], "certifications": ["Robotics Workshop , Newton School of Technology, Rishihood University ( Link ) January 2024. Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functional gesture-controlled wheelchair prototype."], "domain_of_interest": ["AI", "ML", "Web Development", "Robotics"], "languages_known": ["English", "Hindi"], "social_media": ["https://github.com", "https://www.linkedin.com/in/raman<PERSON><PERSON>ch", "https://www.codechef.com/users/ramanluhach", "https://codeforces.com/profile/<PERSON><PERSON><PERSON><PERSON><PERSON>", "https://leetcode.com/Raman<PERSON>uhach/"], "achievements": ["Solved more then 400 questions on LeetCode", "1053 Rank inIEEEXtreme contest", "Holds highest ratings of 1592 on CodeChef ,1618 on LeetCode , and 1211 on Codeforces", "2nd place in an inter-university kabaddi tournament", "Led a team of developers in organizing tech events, facilitated workshops, and mentored junior members."], "publications": [], "volunteer_experience": [], "references": [], "summary": "Dedicated and enthusiastic Front End Developer with a strong aptitude for coding and a passion for creating exceptional user experiences.", "confidence_score": 0.62, "confidence_details": {}, "processing_time": 16.716063022613525, "extraction_method": "hybrid_regex_llm", "sections_extracted": 7, "regex_confidence": 0.9285714285714286}