{"event": "session_start", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "timestamp": "2025-07-02T17:31:01.371305", "message": "New API session started"}
{"event": "request_start", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "request_id": "4d055e6f-9cff-40d4-bac7-f9f13a6cfd34", "endpoint": "/", "timestamp": "2025-07-02T17:31:03.337500", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "request_id": "4d055e6f-9cff-40d4-bac7-f9f13a6cfd34", "endpoint": "/", "timestamp": "2025-07-02T17:31:03.338496", "total_time_seconds": 0.0009961128234863281, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "request_id": "7835dec6-7f11-42af-804d-b0686793f591", "endpoint": "/docs", "timestamp": "2025-07-02T17:31:12.546885", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "request_id": "7835dec6-7f11-42af-804d-b0686793f591", "endpoint": "/docs", "timestamp": "2025-07-02T17:31:12.547891", "total_time_seconds": 0.001005411148071289, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "request_id": "cdf52d0d-5561-460b-9c34-4819c015ed87", "endpoint": "/openapi.json", "timestamp": "2025-07-02T17:31:12.623253", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "request_id": "cdf52d0d-5561-460b-9c34-4819c015ed87", "endpoint": "/openapi.json", "timestamp": "2025-07-02T17:31:12.652347", "total_time_seconds": 0.02909398078918457, "status_code": 200, "message": "Request completed in 0.0291s with status 200"}
{"event": "session_end", "session_id": "a0b1de53-a2c7-4e71-b31d-625aef55e9ca", "timestamp": "2025-07-02T18:08:08.095852", "message": "API session ended"}
