{"name": "<PERSON>", "email": "<EMAIL>", "phone": "+918590499109", "education": [{"degree": "B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)", "institution": "<PERSON> Faculty of Engineering and Technology, Chennai,IN", "year": "Sept 2021 - Jun 2025"}], "skills": {"Python": "Used in project: Audio Based Emotion Recognition System", "SQL": "Mentioned in resume", "Tableau": "Mentioned in resume", "HTML": "Mentioned in resume", "CSS": "Mentioned in resume", "TensorFlow": "Used as Artificial Intelligence Intern at Bay of Bengal Programme Intergovernmental Organization - Remote", "PyTorch": "Used as Artificial Intelligence Intern at Bay of Bengal Programme Intergovernmental Organization - Remote", "Pandas": "Used as Machine Learning Intern at Zoftcare Solutions - Tirur, IN", "NumPy": "Used as Machine Learning Intern at Zoftcare Solutions - Tirur, IN", "OpenCV": "Used as Artificial Intelligence Intern at Bay of Bengal Programme Intergovernmental Organization - Remote", "Faster R-CNN": "Used as Artificial Intelligence Intern at Bay of Bengal Programme Intergovernmental Organization - Remote", "YOLO": "Used as Artificial Intelligence Intern at Bay of Bengal Programme Intergovernmental Organization - Remote", "BERT": "Used in project: Topic Based Analysis of Amazon Customer Feedback", "LSTM": "Used in project: Toxic Comment Identification System", "Cosine Similarity": "Used as Machine Learning Intern at Zoftcare Solutions - Tirur, IN", "Flask": "Used as Machine Learning Intern at Zoftcare Solutions - Tirur, IN", "Selenium": "Used in project: Topic Based Analysis of Amazon Customer Feedback", "BeautifulSoup": "Used in project: Topic Based Analysis of Amazon Customer Feedback", "BERTopic": "Used in project: Topic Based Analysis of Amazon Customer Feedback", "LDAModel": "Used in project: Topic Based Analysis of Amazon Customer Feedback", "nltk": "Used in project: Topic Based Analysis of Amazon Customer Feedback", "spacy": "Used in project: Topic Based Analysis of Amazon Customer Feedback"}, "experience": [{"company_name": "Bay of Bengal Programme Intergovernmental Organization - Remote", "role": "Artificial Intelligence Intern", "duration": "February 2025 - Current", "key_responsibilities": "Developed deep learning models for satellite image analysis, including ship detection, length estimation, and potential color detection (Faster R-CNN ResNet-50, 0.93 accuracy).\nWorked on ship cluster analysis, extracting latitude, longitude, and ship count.\nTools and Techniques Used: Faster R-CNN, YOLO, CNN, OpenCV, TensorFlow, PyTorch, Pandas, NumPy"}, {"company_name": "Zoftcare Solutions - Tirur, IN", "role": "Machine Learning Intern", "duration": "August 2023 - October 2023", "key_responsibilities": "Developed a book and movie recommendation system using machine learning algorithms and optimised model performance through data preprocessing and feature engineering.\nTools and Techniques Used: Numpy, Pandas, Cosine Similarity, Flask"}], "projects": [{"name": "Toxic Comment Identification System", "description": "Developed and trained a Bidirectional LSTM model for multi-label text classification, achieving 80% validation accuracy.\nEvaluated and deployed the model using precision, recall, and accuracy metrics for toxic comment identification.\nTools and Techniques Used: Pandas, Numpy, Tensorflow, LSTM"}, {"name": "Audio Based Emotion Recognition System", "description": "Developed a speech emotion recognition system achieving 94% accuracy, leveraging deep learning techniques to analyze audio waveforms and classify emotional states.\nImplemented an end-to-end machine learning pipeline, encompassing data collection, preprocessing, feature extraction (MFCC), model training, and evaluation, resulting in effective emotion classification from speech data.\nTools and Techniques Used: Python, Librosa, NumPy, Pandas, Scikit-learn, TensorFlow/Keras"}, {"name": "Topic Based Analysis of Amazon Customer Feedback", "description": "Conducted detailed analysis of Amazon product reviews to identify recurrent topics and sentiments, enabling a deeper understanding of customer feedback and pain points.\nEmployed topic modeling to extract actionable insights from unstructured textual data, guiding product improvement and enhancing customer satisfaction strategies based on discovered patterns.\nTools and Techniques Used: Selenium, BeautifulSoup, Pandas, BERTopic, LDAModel, nltk, spacy"}], "certifications": [], "domain_of_interest": ["Artificial Intelligence", "Machine Learning", "Deep Learning", "Natural Language Processing", "Computer Vision"], "languages_known": ["English", "Malayalam"], "social_media": [], "achievements": [], "publications": [], "volunteer_experience": [], "references": [], "summary": null, "confidence_score": 0.76, "confidence_details": {}, "processing_time": 35.60359859466553, "extraction_method": "hybrid_regex_llm", "sections_extracted": 8, "regex_confidence": 0.8937499999999999}