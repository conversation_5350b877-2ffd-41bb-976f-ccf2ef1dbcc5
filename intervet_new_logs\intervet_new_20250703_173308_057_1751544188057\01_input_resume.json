{"name": "<PERSON><PERSON>", "email": null, "phone": null, "education": [{"degree": "Bachelor of Technology (AI ML)", "institution": "Newton School of Technology , Rishihood University", "year": "2023 - 2027"}, {"degree": "Intermediate", "institution": "Mother India Sr Sec School Marot, Jhajjar , Haryana", "year": "2021 - 2022"}, {"degree": "Matriculation", "institution": "R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana", "year": "2019 - 2020"}], "skills": ["SQL", "Java", "JavaScript", "CSS", "HTML", "Python", "React", "MySQL", "NodeJS", "Prisma ORM", "Tailwind", "Data Structure"], "experience": [{"company_name": "Google Developer Groups (GDG) Rishihood University", "role": "Tech Lead", "duration": null, "key_responsibilities": "Led a team of developers at GDG Rishihood University, contributing to tech talks and workshops. Mentored other students in coding and development practices."}], "projects": [{"name": "Tech Talks", "description": "Developed \"Tech Talks\", a blogging site for tech content. Users can register, log in, create, access and comment on tech blogs . Tech Stack: React ,Node .js,HTML /CSS"}, {"name": "Zee5 Clone", "description": "Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access a library of movies with intuitive UI. Feature: Movie rendering , Authentication. Tech Stack: React ,HTML /CSS andJavaScript"}], "certifications": [], "domain_of_interest": [], "languages_known": [], "achievements": [], "publications": [], "volunteer_experience": [], "references": [], "summary": null, "personal_projects": [], "social_media": [], "processing_time": 31.22638702392578, "extraction_method": "hybrid_regex_llm", "sections_extracted": 7, "regex_confidence": 0.9285714285714286}