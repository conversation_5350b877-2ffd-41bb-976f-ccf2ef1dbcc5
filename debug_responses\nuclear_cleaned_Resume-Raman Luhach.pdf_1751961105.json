{"name": "<PERSON><PERSON>", "email": null, "phone": null, "education": [{"degree": "Bachelor of Technology (AI ML)", "institution": "Newton School of Technology, Rishihood University", "year": "2023 - 2027", "grade": "9.18/10.0"}, {"degree": "Intermediate", "institution": "Mother India Sr Sec School Marot, Jhajjar , Haryana", "year": "2021 - 2022", "grade": "90.0%"}, {"degree": "Matriculation", "institution": "R E D Sr Sec School Chhuchhakwas ,Jhajjar , Haryana", "year": "2019 - 2020", "grade": "95.8%"}], "highest_education": "Bachelor of Technology (AI ML)", "skills": ["SQL", "Java", "JavaScript", "CSS", "HTML", "Python", "React", "MySQL", "NodeJS", "Prisma ORM", "Tailwind", "Data Structure", "Communication Skills", "Research", "Decision-making", "Team Building", "Leadership"], "experience": [{"company_name": "Google Developer Groups (GDG) Rishihood University", "role": "Tech Lead", "duration": "Present", "key_responsibilities": "Led tech-focused groups within the university community, organizing workshops, hackathons, and technical events. Collaborated with students and faculty to explore and implement innovative technology solutions."}], "projects": [{"name": "Tech Talks", "description": "Developed \"Tech Talks\", a blogging site for tech content. Users can register, log in, create, access, and comment on tech blogs. The tech stack includes React, Node.js, HTML, and CSS. Features include user authentication, blog management, categorized content, and CRUD operations on blogs."}, {"name": "Zee5 Clone", "description": "Created a Zee5 clone using React, HTML/CSS, and JavaScript. Users register, sign in securely, and access a library of movies with an intuitive UI. Features include movie rendering and secure authentication."}], "certifications": ["Robotics Workshop, Newton School of Technology, Rishihood University ( Link ) January 2024: Participated in a 5-day workshop on Arduino Uno fundamentals, DC motor functions, and joystick operations. Developed a functional gesture-controlled wheelchair prototype."], "domain_of_interest": ["AI", "ML", "Robotics", "Web Development"], "languages_known": ["English", "Hindi"], "achievements": ["Solved more than 400 questions on LeetCode", "1053 Rank in IEEE Xtreme contest", "Holds highest ratings of 1592 on CodeChef, 1618 on LeetCode, and 1211 on Codeforces", "2nd place in an inter-university kabaddi tournament", "Led tech-focused groups within the university community, organizing workshops, hackathons, and technical events."], "publications": [], "volunteer_experience": [], "references": [], "summary": "Dedicated and enthusiastic Front End Developer with a strong aptitude for coding and a passion for creating exceptional user experiences.", "personal_projects": [], "social_media": ["https://www.linkedin.com/in/raman<PERSON><PERSON>ch", "https://github.com/ramanluhach", "https://codechef.com/user/raman<PERSON><PERSON>ch", "https://codeforces.com/profile/raman<PERSON><PERSON>ch", "https://leetcode.com/user/raman<PERSON><PERSON>ch/"]}