{"total_score": 5.839160839160839, "fit_category": "Good Match", "summary": "The candidate is a good match for this position with a CGPA-style score of 5.8/10. Key strengths: Education. Areas for improvement: Skills, Certifications.", "skills_score": {"raw_score": 2.1818181818181817, "weight": 5.0, "weighted_score": 10.909090909090908, "rationale": "Limited skills match - has 3/11 required skills, major skill development needed"}, "experience_score": {"raw_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Moderate experience level - 0 years, some experience gaps"}, "education_score": {"raw_score": 10.0, "weight": 5.0, "weighted_score": 50.0, "rationale": "Perfect education match - meets all degree requirements"}, "certifications_score": {"raw_score": 0.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "No certifications listed"}, "location_score": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate location match - some geographic alignment"}, "reliability_score": {"raw_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Moderate job stability - average 0.0 years per company"}, "total_credits_used": 13.0, "calculation_method": "CGPA", "processing_time": 0.006997823715209961}