{"job_title": "Software Engineer Trainee", "company_name": "Vyoma Linguistic Labs Foundation", "location": null, "job_type": "Part-time/ Full-time", "work_mode": "On-site", "department": null, "summary": "Vyoma Linguistic Labs Foundation is seeking a Software Engineer Trainee to join their team, contributing to software development and testing activities.", "responsibilities": ["Gathering user requirements", "Defining system functionality", "Writing code in various languages like HTML, CSS, Javascript and Media queries", "Troubleshooting, debug, and resolve software defects and issues", "Contributing creative solutions to technical challenges", "Collaborating with senior engineers to design, code, and test software applications", "Contributing to the development of high -quality, scalable, and maintainable code", "Assisting in the testing process by writing and executing  test cases", "Identifying and reporting bugs and work towards their resolution", "Maintaining thorough documentation of code, design, and testing activities", "Ensuring that documentation is accurate, up -to-date, and accessible to team members", "Adapt quickly to new technologies and programming languages as required by the project", "Taking initiative in continuous self -improvement, staying informed about advancements in software development and applying new knowledge to yo ur work"], "required_skills": ["HTML", "CSS", "Javascript", "Media queries", "SDLC"], "preferred_skills": ["Sanskrit"], "required_experience": "1 year", "education_requirements": ["Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field"], "education_details": {"degree_level": "Bachelor’s", "field_of_study": "IT, Computer Science, Software Engineering, or a related field", "is_required": true, "alternatives": null}, "salary_range": null, "benefits": [], "requirements": [{"title": "Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field", "description": "A bachelor’s degree in the specified field is required.", "is_mandatory": true}, {"title": "Basic understanding of programming languages such as HTML, CSS, Javascript and Media Queries", "description": "Must have a basic understanding of these programming languages.", "is_mandatory": true}, {"title": "Good written and verbal communication skills in English", "description": "Must possess strong communication skills in English.", "is_mandatory": true}], "application_deadline": null, "posting_date": null, "contact_information": null, "company_description": null, "industry": "Education, Non-profit", "career_level": "Entry", "confidence_score": 0.86, "confidence_details": {}}