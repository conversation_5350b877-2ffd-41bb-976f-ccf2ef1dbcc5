{"event": "session_start", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "timestamp": "2025-07-08T12:44:30.497266", "message": "New API session started"}
{"event": "request_start", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "1416073e-87b7-48a3-b61c-9775f5b668ce", "endpoint": "/", "timestamp": "2025-07-08T12:44:32.805536", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "1416073e-87b7-48a3-b61c-9775f5b668ce", "endpoint": "/", "timestamp": "2025-07-08T12:44:32.807534", "total_time_seconds": 0.0019979476928710938, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "1f5b8b23-99c5-4ada-92b8-9d64c101d636", "endpoint": "/favicon.ico", "timestamp": "2025-07-08T12:44:32.887054", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "1f5b8b23-99c5-4ada-92b8-9d64c101d636", "endpoint": "/favicon.ico", "timestamp": "2025-07-08T12:44:32.888056", "total_time_seconds": 0.0010020732879638672, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "a0d5fd50-5a6d-4bd6-86c8-a08e9dc58b94", "endpoint": "/docs", "timestamp": "2025-07-08T12:44:35.907687", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "a0d5fd50-5a6d-4bd6-86c8-a08e9dc58b94", "endpoint": "/docs", "timestamp": "2025-07-08T12:44:35.908688", "total_time_seconds": 0.0010008811950683594, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "5a85c637-8d8c-4e9a-b1ba-b5b03d5a6cfe", "endpoint": "/openapi.json", "timestamp": "2025-07-08T12:44:35.974790", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "5a85c637-8d8c-4e9a-b1ba-b5b03d5a6cfe", "endpoint": "/openapi.json", "timestamp": "2025-07-08T12:44:35.990809", "total_time_seconds": 0.016019105911254883, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
{"event": "request_start", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.869391", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.871383", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.880464", "final_score": 5.67, "message": "Custom metric: final_score=5.67"}
{"event": "custom_metric", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.880464", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.880464", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.880464", "log_folder": "intervet_new_logs\\intervet_new_20250708_124619_874_1751958979874", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_124619_874_1751958979874"}
{"event": "request_complete", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "request_id": "9a423124-59ce-4694-9820-6f40886f34f0", "endpoint": "/intervet_new", "timestamp": "2025-07-08T12:46:19.881463", "total_time_seconds": 0.012072086334228516, "status_code": 200, "message": "Request completed in 0.0121s with status 200"}
{"event": "session_end", "session_id": "a07f5e5d-2a52-479e-b5ad-765959dfed77", "timestamp": "2025-07-08T13:03:39.946785", "message": "API session ended"}
