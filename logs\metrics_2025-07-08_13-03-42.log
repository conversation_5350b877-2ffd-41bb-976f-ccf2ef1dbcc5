{"event": "session_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "timestamp": "2025-07-08T13:03:42.795289", "message": "New API session started"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "7bd8e5e2-0844-406c-8992-bc1d815bfb68", "endpoint": "/", "timestamp": "2025-07-08T13:03:44.142058", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "7bd8e5e2-0844-406c-8992-bc1d815bfb68", "endpoint": "/", "timestamp": "2025-07-08T13:03:44.143056", "total_time_seconds": 0.0009987354278564453, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "d39c3a11-99e3-4b7b-8b5d-cb0e940e68f0", "endpoint": "/docs", "timestamp": "2025-07-08T13:03:46.225808", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "d39c3a11-99e3-4b7b-8b5d-cb0e940e68f0", "endpoint": "/docs", "timestamp": "2025-07-08T13:03:46.225808", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "f1c4f39c-38a7-495a-87a1-491cbc1a1759", "endpoint": "/openapi.json", "timestamp": "2025-07-08T13:03:46.299325", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "f1c4f39c-38a7-495a-87a1-491cbc1a1759", "endpoint": "/openapi.json", "timestamp": "2025-07-08T13:03:46.315325", "total_time_seconds": 0.015999794006347656, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
