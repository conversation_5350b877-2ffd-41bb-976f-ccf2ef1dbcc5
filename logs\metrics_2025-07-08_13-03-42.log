{"event": "session_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "timestamp": "2025-07-08T13:03:42.795289", "message": "New API session started"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "7bd8e5e2-0844-406c-8992-bc1d815bfb68", "endpoint": "/", "timestamp": "2025-07-08T13:03:44.142058", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "7bd8e5e2-0844-406c-8992-bc1d815bfb68", "endpoint": "/", "timestamp": "2025-07-08T13:03:44.143056", "total_time_seconds": 0.0009987354278564453, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "d39c3a11-99e3-4b7b-8b5d-cb0e940e68f0", "endpoint": "/docs", "timestamp": "2025-07-08T13:03:46.225808", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "d39c3a11-99e3-4b7b-8b5d-cb0e940e68f0", "endpoint": "/docs", "timestamp": "2025-07-08T13:03:46.225808", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "f1c4f39c-38a7-495a-87a1-491cbc1a1759", "endpoint": "/openapi.json", "timestamp": "2025-07-08T13:03:46.299325", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "f1c4f39c-38a7-495a-87a1-491cbc1a1759", "endpoint": "/openapi.json", "timestamp": "2025-07-08T13:03:46.315325", "total_time_seconds": 0.015999794006347656, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.894597", "message": "Request started for endpoint: /resume"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "resume", "timestamp": "2025-07-08T13:04:17.899596", "message": "Custom metric: endpoint=resume"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.899596", "file_name": "Resume-Sharath Kumar.R.pdf", "message": "Custom metric: file_name=Resume-Sharath Kumar.R.pdf"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.899596", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.901595", "file_size": 72406, "message": "Custom metric: file_size=72406"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.930596", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.930596", "file_size_bytes": 72406, "message": "Custom metric: file_size_bytes=72406"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.930596", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.930596", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.931597", "file_processing_time": 0.02799820899963379, "message": "Custom metric: file_processing_time=0.02799820899963379"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.931597", "text_extraction_time": 0.02899932861328125, "message": "Custom metric: text_extraction_time=0.02899932861328125"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:17.931597", "extracted_text_length": 2916, "message": "Custom metric: extracted_text_length=2916"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.944151", "parsing_time": 32.012553691864014, "message": "Custom metric: parsing_time=32.012553691864014"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.944151", "confidence_score": 0.33, "message": "Custom metric: confidence_score=0.33"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.944151", "fields_extracted": 20, "message": "Custom metric: fields_extracted=20"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.944151", "skills_count": 14, "message": "Custom metric: skills_count=14"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.944151", "education_count": 1, "message": "Custom metric: education_count=1"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.944151", "experience_count": 1, "message": "Custom metric: experience_count=1"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.945150", "total_processing_time": 32.041553020477295, "message": "Custom metric: total_processing_time=32.041553020477295"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "8960ad19-51e7-4514-819c-14cf32f557f6", "endpoint": "/resume", "timestamp": "2025-07-08T13:04:49.946150", "total_time_seconds": 32.05155372619629, "status_code": 200, "message": "Request completed in 32.0516s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.087971", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.088969", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.098972", "final_score": 5.67, "message": "Custom metric: final_score=5.67"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.099969", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.099969", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.099969", "log_folder": "intervet_new_logs\\intervet_new_20250708_130617_093_1751960177093", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_130617_093_1751960177093"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "0460496e-a3c0-46c6-9c3e-a2316d0f1d1a", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:17.100970", "total_time_seconds": 0.012999296188354492, "status_code": 200, "message": "Request completed in 0.0130s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.447177", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.448179", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.457183", "final_score": 5.67, "message": "Custom metric: final_score=5.67"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.457183", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.457183", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.457183", "log_folder": "intervet_new_logs\\intervet_new_20250708_130618_450_1751960178450", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250708_130618_450_1751960178450"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "682d6208-0d96-497e-a01a-d8ca400e478f", "endpoint": "/intervet_new", "timestamp": "2025-07-08T13:06:18.458184", "total_time_seconds": 0.011006832122802734, "status_code": 200, "message": "Request completed in 0.0110s with status 200"}
{"event": "request_start", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:43.162748", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:43.184753", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:43.184753", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:43.184753", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:43.184753", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:43.184753", "file_processing_time": 0.019006729125976562, "message": "Custom metric: file_processing_time=0.019006729125976562"}
{"event": "request_complete", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "request_id": "435bc155-f990-421c-b79c-5601d01f4f21", "endpoint": "/hybrid_resume", "timestamp": "2025-07-08T13:09:59.435606", "total_time_seconds": 16.272857666015625, "status_code": 200, "message": "Request completed in 16.2729s with status 200"}
{"event": "session_end", "session_id": "da6c48ac-0790-4cb4-84dd-736354ae1f87", "timestamp": "2025-07-08T13:13:42.275937", "message": "API session ended"}
